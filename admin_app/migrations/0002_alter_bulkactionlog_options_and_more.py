# Generated by Django 5.2.3 on 2025-07-06 06:23

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('admin_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='bulkactionlog',
            options={'ordering': ['-timestamp'], 'verbose_name': 'Bulk Action Log', 'verbose_name_plural': 'Bulk Action Logs'},
        ),
        migrations.RemoveField(
            model_name='bulkactionlog',
            name='affected_ids',
        ),
        migrations.RemoveField(
            model_name='bulkactionlog',
            name='affected_model',
        ),
        migrations.RemoveField(
            model_name='bulkactionlog',
            name='description',
        ),
        migrations.RemoveField(
            model_name='bulkactionlog',
            name='executed_at',
        ),
        migrations.RemoveField(
            model_name='bulkactionlog',
            name='executed_by',
        ),
        migrations.RemoveField(
            model_name='bulkactionlog',
            name='ip_address',
        ),
        migrations.RemoveField(
            model_name='bulkactionlog',
            name='user_agent',
        ),
        migrations.AddField(
            model_name='bulkactionlog',
            name='admin_user',
            field=models.ForeignKey(default=1, help_text='Admin user who performed the bulk action', on_delete=django.db.models.deletion.CASCADE, related_name='bulk_actions_performed', to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='bulkactionlog',
            name='details',
            field=models.JSONField(blank=True, default=dict, help_text='Additional details about the bulk action in JSON format'),
        ),
        migrations.AddField(
            model_name='bulkactionlog',
            name='error_message',
            field=models.TextField(blank=True, help_text='Error message if the bulk action failed'),
        ),
        migrations.AddField(
            model_name='bulkactionlog',
            name='model_name',
            field=models.CharField(default='Unknown', help_text='Name of the model affected by the bulk action', max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='bulkactionlog',
            name='success',
            field=models.BooleanField(default=True, help_text='Whether the bulk action completed successfully'),
        ),
        migrations.AddField(
            model_name='bulkactionlog',
            name='timestamp',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, help_text='When the bulk action was performed'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='bulkactionlog',
            name='action_type',
            field=models.CharField(choices=[('bulk_activate', 'Bulk Activate'), ('bulk_deactivate', 'Bulk Deactivate'), ('bulk_export_csv', 'Bulk Export CSV'), ('bulk_validate_data', 'Bulk Validate Data'), ('bulk_send_welcome_email', 'Bulk Send Welcome Email'), ('bulk_reset_passwords', 'Bulk Reset Passwords'), ('bulk_verify_emails', 'Bulk Verify Emails'), ('bulk_feature_venues', 'Bulk Feature Venues'), ('bulk_unfeature_venues', 'Bulk Unfeature Venues'), ('bulk_update_pricing', 'Bulk Update Pricing'), ('bulk_confirm_bookings', 'Bulk Confirm Bookings'), ('bulk_cancel_bookings', 'Bulk Cancel Bookings'), ('bulk_send_reminders', 'Bulk Send Reminders')], help_text='Type of bulk action performed', max_length=50),
        ),
        migrations.AlterField(
            model_name='bulkactionlog',
            name='affected_count',
            field=models.PositiveIntegerField(help_text='Number of objects affected by the bulk action'),
        ),
    ]
