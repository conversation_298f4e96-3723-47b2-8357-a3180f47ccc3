"""Modern coupon and promotion models for CozyWish discount system."""

import uuid
import secrets
import string
from decimal import Decimal
from typing import List, Dict, Any, Optional
from enum import Enum

from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator

from djmoney.models.fields import MoneyField
from djmoney.money import Money
from django_lifecycle import LifecycleModel, hook, AFTER_CREATE, BEFORE_SAVE
from pydantic import BaseModel, Field, validator
from django_pydantic_field import SchemaField

from venues_app.models import Category, Service, Venue


class CouponType(models.TextChoices):
    """Types of coupons available in the system."""
    PERCENTAGE = "percentage", _("Percentage Discount")
    FIXED_AMOUNT = "fixed_amount", _("Fixed Amount Discount")
    FREE_SHIPPING = "free_shipping", _("Free Shipping")
    BUY_X_GET_Y = "buy_x_get_y", _("Buy X Get Y Free")
    FIRST_TIME_USER = "first_time_user", _("First Time User Discount")


class CouponStatus(models.TextChoices):
    """Status options for coupons."""
    DRAFT = "draft", _("Draft")
    ACTIVE = "active", _("Active")
    PAUSED = "paused", _("Paused")
    EXPIRED = "expired", _("Expired")
    DEPLETED = "depleted", _("Depleted")


class DiscountTarget(models.TextChoices):
    """What the discount applies to."""
    ORDER_TOTAL = "order_total", _("Order Total")
    SPECIFIC_SERVICES = "specific_services", _("Specific Services")
    CATEGORY = "category", _("Category")
    VENUE = "venue", _("Venue")
    SHIPPING = "shipping", _("Shipping")


# Pydantic schemas for business rules validation
class CouponRulesSchema(BaseModel):
    """Pydantic schema for coupon business rules validation."""
    min_order_amount: Optional[Decimal] = Field(None, ge=0, description="Minimum order amount")
    max_order_amount: Optional[Decimal] = Field(None, ge=0, description="Maximum order amount")
    min_items: Optional[int] = Field(None, ge=1, description="Minimum number of items")
    max_items: Optional[int] = Field(None, ge=1, description="Maximum number of items")
    allowed_categories: Optional[List[int]] = Field(None, description="Allowed category IDs")
    excluded_categories: Optional[List[int]] = Field(None, description="Excluded category IDs")
    allowed_venues: Optional[List[int]] = Field(None, description="Allowed venue IDs")
    excluded_venues: Optional[List[int]] = Field(None, description="Excluded venue IDs")
    allowed_services: Optional[List[int]] = Field(None, description="Allowed service IDs")
    excluded_services: Optional[List[int]] = Field(None, description="Excluded service IDs")
    user_restrictions: Optional[Dict[str, Any]] = Field(None, description="User-based restrictions")
    time_restrictions: Optional[Dict[str, Any]] = Field(None, description="Time-based restrictions")
    combination_rules: Optional[Dict[str, Any]] = Field(None, description="Coupon combination rules")

    @validator('max_order_amount')
    def validate_max_order_amount(cls, v, values):
        """Ensure max_order_amount is greater than min_order_amount."""
        if v is not None and 'min_order_amount' in values and values['min_order_amount'] is not None:
            if v <= values['min_order_amount']:
                raise ValueError('max_order_amount must be greater than min_order_amount')
        return v

    @validator('max_items')
    def validate_max_items(cls, v, values):
        """Ensure max_items is greater than min_items."""
        if v is not None and 'min_items' in values and values['min_items'] is not None:
            if v <= values['min_items']:
                raise ValueError('max_items must be greater than min_items')
        return v


class PromotionConditionsSchema(BaseModel):
    """Pydantic schema for promotion conditions."""
    condition_type: str = Field(..., description="Type of condition")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Condition parameters")
    logical_operator: str = Field("AND", description="Logical operator for multiple conditions")

    @validator('condition_type')
    def validate_condition_type(cls, v):
        """Validate condition type."""
        allowed_types = [
            'min_amount', 'max_amount', 'user_type', 'first_time_user',
            'category_match', 'venue_match', 'service_match',
            'time_range', 'day_of_week', 'usage_count'
        ]
        if v not in allowed_types:
            raise ValueError(f'condition_type must be one of {allowed_types}')
        return v


class CouponCode(LifecycleModel):
    """Modern coupon code model with enhanced features."""

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(
        max_length=50,
        unique=True,
        help_text=_("Unique coupon code (auto-generated if not provided)")
    )
    name = models.CharField(
        max_length=255,
        help_text=_("Internal name for the coupon")
    )
    description = models.TextField(
        blank=True,
        help_text=_("Description of what this coupon offers")
    )

    # Coupon Configuration
    coupon_type = models.CharField(
        max_length=20,
        choices=CouponType.choices,
        default=CouponType.PERCENTAGE,
        help_text=_("Type of discount this coupon provides")
    )
    status = models.CharField(
        max_length=20,
        choices=CouponStatus.choices,
        default=CouponStatus.DRAFT,
        help_text=_("Current status of the coupon")
    )
    target = models.CharField(
        max_length=20,
        choices=DiscountTarget.choices,
        default=DiscountTarget.ORDER_TOTAL,
        help_text=_("What this discount applies to")
    )

    # Discount Values (using MoneyField for precision)
    discount_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text=_("Discount value (percentage or amount)")
    )
    max_discount_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        null=True,
        blank=True,
        help_text=_("Maximum discount amount (for percentage coupons)")
    )

    # Usage Limits
    usage_limit = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text=_("Maximum number of times this coupon can be used")
    )
    usage_limit_per_user = models.PositiveIntegerField(
        default=1,
        help_text=_("Maximum uses per user")
    )
    current_usage_count = models.PositiveIntegerField(
        default=0,
        help_text=_("Current number of times this coupon has been used")
    )

    # Date/Time Restrictions
    valid_from = models.DateTimeField(
        help_text=_("When this coupon becomes valid")
    )
    valid_until = models.DateTimeField(
        help_text=_("When this coupon expires")
    )

    # Business Rules (using Pydantic schema)
    business_rules: CouponRulesSchema = SchemaField(schema=CouponRulesSchema)

    # Relationships
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_coupons",
        help_text=_("User who created this coupon")
    )
    allowed_users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name="allowed_coupons",
        help_text=_("Specific users who can use this coupon (empty = all users)")
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_stackable = models.BooleanField(
        default=False,
        help_text=_("Can this coupon be combined with other discounts?")
    )
    priority = models.PositiveIntegerField(
        default=1,
        help_text=_("Priority for applying multiple coupons (higher = first)")
    )

    class Meta:
        ordering = ['-priority', '-created_at']
        verbose_name = _("Coupon Code")
        verbose_name_plural = _("Coupon Codes")
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['status', 'valid_from', 'valid_until']),
            models.Index(fields=['coupon_type', 'target']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    @hook(BEFORE_SAVE)
    def generate_code_if_empty(self):
        """Auto-generate coupon code if not provided."""
        if not self.code:
            self.code = self.generate_unique_code()

    @hook(AFTER_CREATE)
    def log_coupon_creation(self):
        """Log coupon creation event."""
        from utils.logging_utils import log_user_activity
        log_user_activity(
            app_name="discount_app",
            activity_type="coupon_created",
            user=self.created_by,
            details={
                "coupon_id": str(self.id),
                "coupon_code": self.code,
                "coupon_type": self.coupon_type,
                "discount_value": float(self.discount_value),
            }
        )

    def clean(self):
        """Custom validation for coupon models."""
        super().clean()
        
        # Validate date range
        if self.valid_from and self.valid_until:
            if self.valid_from >= self.valid_until:
                raise ValidationError(_("Valid from date must be before valid until date"))

        # Validate discount value based on type
        if self.coupon_type == CouponType.PERCENTAGE:
            if self.discount_value <= 0 or self.discount_value > 100:
                raise ValidationError(_("Percentage discount must be between 0.01 and 100"))
        elif self.coupon_type == CouponType.FIXED_AMOUNT:
            if self.discount_value <= 0:
                raise ValidationError(_("Fixed amount discount must be greater than 0"))

        # Validate usage limits
        if self.usage_limit and self.usage_limit <= 0:
            raise ValidationError(_("Usage limit must be greater than 0"))

    @staticmethod
    def generate_unique_code(length=8, prefix="COZY"):
        """Generate a unique coupon code."""
        alphabet = string.ascii_uppercase + string.digits
        code = prefix + ''.join(secrets.choice(alphabet) for _ in range(length))
        
        # Ensure uniqueness
        while CouponCode.objects.filter(code=code).exists():
            code = prefix + ''.join(secrets.choice(alphabet) for _ in range(length))
        
        return code

    def is_valid(self, user=None, cart_total=None):
        """Check if coupon is currently valid for use."""
        now = timezone.now()
        
        # Check status
        if self.status != CouponStatus.ACTIVE:
            return False, f"Coupon is {self.status}"
        
        # Check date range
        if now < self.valid_from:
            return False, "Coupon is not yet valid"
        
        if now > self.valid_until:
            return False, "Coupon has expired"
        
        # Check usage limits
        if self.usage_limit and self.current_usage_count >= self.usage_limit:
            return False, "Coupon usage limit reached"
        
        # Check user-specific limits
        if user and self.usage_limit_per_user:
            user_usage = CouponUsage.objects.filter(
                coupon=self, user=user
            ).count()
            if user_usage >= self.usage_limit_per_user:
                return False, "Personal usage limit reached"
        
        # Check user restrictions
        if self.allowed_users.exists() and user:
            if not self.allowed_users.filter(id=user.id).exists():
                return False, "Coupon not available for this user"
        
        return True, "Valid"

    def calculate_discount(self, order_total, items=None):
        """Calculate the discount amount for a given order."""
        if self.coupon_type == CouponType.PERCENTAGE:
            discount = (order_total * self.discount_value) / 100
            if self.max_discount_amount:
                discount = min(discount, self.max_discount_amount.amount)
        elif self.coupon_type == CouponType.FIXED_AMOUNT:
            discount = min(self.discount_value, order_total)
        else:
            # For other types, return 0 for now
            discount = Decimal('0.00')
        
        return max(discount, Decimal('0.00'))

    def increment_usage(self):
        """Increment the usage count."""
        self.current_usage_count = models.F('current_usage_count') + 1
        self.save(update_fields=['current_usage_count'])


class CouponUsage(models.Model):
    """Track individual coupon usage instances."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    coupon = models.ForeignKey(
        CouponCode,
        on_delete=models.CASCADE,
        related_name="usage_instances"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="coupon_usage_history"
    )
    
    # Order information (will be linked when booking system is implemented)
    order_reference = models.CharField(
        max_length=100,
        help_text=_("Reference to the order where this coupon was used")
    )
    
    # Financial details
    original_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        help_text=_("Original order amount before discount")
    )
    discount_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        help_text=_("Amount of discount applied")
    )
    final_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        help_text=_("Final order amount after discount")
    )
    
    # Metadata
    used_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-used_at']
        verbose_name = _("Coupon Usage")
        verbose_name_plural = _("Coupon Usage Records")
        indexes = [
            models.Index(fields=['coupon', 'user']),
            models.Index(fields=['used_at']),
            models.Index(fields=['order_reference']),
        ]

    def __str__(self):
        return f"{self.coupon.code} used by {self.user.username} on {self.used_at}"


class PromotionCampaign(LifecycleModel):
    """Advanced promotion campaign with complex business rules."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, help_text=_("Campaign name"))
    slug = models.SlugField(max_length=255, unique=True, help_text=_("URL-friendly campaign identifier"))
    description = models.TextField(help_text=_("Campaign description"))
    
    # Campaign status and timing
    status = models.CharField(
        max_length=20,
        choices=CouponStatus.choices,
        default=CouponStatus.DRAFT
    )
    start_date = models.DateTimeField(help_text=_("Campaign start date"))
    end_date = models.DateTimeField(help_text=_("Campaign end date"))
    
    # Promotion rules (using Pydantic schema)
    conditions: List[PromotionConditionsSchema] = SchemaField(schema=List[PromotionConditionsSchema])
    
    # Associated coupons
    coupons = models.ManyToManyField(
        CouponCode,
        blank=True,
        related_name="campaigns",
        help_text=_("Coupons associated with this campaign")
    )
    
    # Campaign metrics
    target_users = models.PositiveIntegerField(
        null=True, blank=True,
        help_text=_("Target number of users for this campaign")
    )
    budget_limit = MoneyField(
        max_digits=12,
        decimal_places=2,
        default_currency='USD',
        null=True, blank=True,
        help_text=_("Maximum budget for this campaign")
    )
    current_spend = MoneyField(
        max_digits=12,
        decimal_places=2,
        default_currency='USD',
        default=Money(0, 'USD'),
        help_text=_("Current amount spent on this campaign")
    )
    
    # Metadata
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_campaigns"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Promotion Campaign")
        verbose_name_plural = _("Promotion Campaigns")

    def __str__(self):
        return self.name

    @hook(AFTER_CREATE)
    def log_campaign_creation(self):
        """Log campaign creation event."""
        from utils.logging_utils import log_user_activity
        log_user_activity(
            app_name="discount_app",
            activity_type="campaign_created",
            user=self.created_by,
            details={
                "campaign_id": str(self.id),
                "campaign_name": self.name,
                "start_date": self.start_date.isoformat(),
                "end_date": self.end_date.isoformat(),
            }
        )

    def is_active(self):
        """Check if campaign is currently active."""
        now = timezone.now()
        return (
            self.status == CouponStatus.ACTIVE and
            self.start_date <= now <= self.end_date
        )

    def budget_remaining(self):
        """Calculate remaining budget."""
        if not self.budget_limit:
            return None
        return self.budget_limit - self.current_spend

    def is_budget_available(self, amount):
        """Check if budget is available for the given amount."""
        remaining = self.budget_remaining()
        if remaining is None:
            return True
        return remaining >= Money(amount, self.budget_limit.currency) 