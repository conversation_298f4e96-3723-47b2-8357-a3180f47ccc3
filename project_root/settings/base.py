# --- Standard Library Imports ---
import os
import sys
from pathlib import Path
import dj_database_url
import environ

# --- Django Imports ---
from django.urls import reverse_lazy
from django.templatetags.static import static

# --- Local App Imports ---
from config.logging import LOGGING

# --- Environment Configuration ---
env = environ.Env(
    # Set casting and default values
    DEBUG=(bool, False),
    SECRET_KEY=(str, ''),
    LOG_LEVEL=(str, 'INFO'),
    PLATFORM_FEE_RATE=(float, 0.05),
    DASHBOARD_CACHE_TIMEOUT=(int, 300),
    NOTIFICATION_CACHE_TIMEOUT=(int, 60),
    EMAIL_PORT=(int, 587),
    EMAIL_USE_TLS=(bool, True),
    FORCE_EMAIL_BACKEND=(bool, False),
)

# --- Base Directory ---
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# --- Read Environment File ---
environ.Env.read_env(BASE_DIR / '.env')



# --- Environment Validation ---
def validate_environment():
    """Validate required environment variables with detailed error messages."""
    errors = []
    warnings = []

    # Required variables for all environments
    required_vars = {
        'SECRET_KEY': 'Django secret key for cryptographic signing',
    }

    # Check required variables
    for var, description in required_vars.items():
        value = env(var, default=None)
        if not value:
            errors.append(f"  - {var}: {description}")
        elif var == 'SECRET_KEY' and value == 'django-insecure-change-this-in-production':
            warnings.append(f"  - {var}: Using default insecure key. Generate a new one for production!")

    # Environment-specific validation
    django_env = env('DJANGO_ENVIRONMENT', default='development')

    if django_env == 'production':
        production_vars = {
            'DATABASE_URL': 'PostgreSQL database connection string',
            'EMAIL_HOST_PASSWORD': 'SendGrid API key for email sending',
            'AWS_ACCESS_KEY_ID': 'AWS access key for S3 storage',
            'AWS_SECRET_ACCESS_KEY': 'AWS secret key for S3 storage',
            'AWS_STORAGE_BUCKET_NAME': 'S3 bucket name for file storage',
        }

        for var, description in production_vars.items():
            value = env(var, default=None)
            if not value:
                errors.append(f"  - {var}: {description} (required in production)")

    # Check for common configuration issues
    debug_mode = env('DEBUG', default=False)
    if django_env == 'production' and debug_mode:
        warnings.append("  - DEBUG: Debug mode is enabled in production environment")

    # Report validation results
    if errors:
        error_msg = f"""
Environment Validation Failed!

Missing required environment variables for '{django_env}' environment:
{chr(10).join(errors)}

Please check your .env file or environment variables.
See .env.example for a complete list of available variables.
"""
        raise environ.ImproperlyConfigured(error_msg)

    if warnings:
        warning_msg = f"""
Environment Validation Warnings for '{django_env}' environment:
{chr(10).join(warnings)}
"""
        print(f"\033[93m{warning_msg}\033[0m")  # Yellow warning text

# Validate environment on import
validate_environment()

# --- Logs Configuration ---
LOG_LEVEL = env('LOG_LEVEL').upper()

# --- Core Configuration ---
SECRET_KEY = env('SECRET_KEY')
DEBUG = env('DEBUG')
PLATFORM_FEE_RATE = env('PLATFORM_FEE_RATE')
DASHBOARD_CACHE_TIMEOUT = env('DASHBOARD_CACHE_TIMEOUT')
NOTIFICATION_CACHE_TIMEOUT = env('NOTIFICATION_CACHE_TIMEOUT')
ENABLE_TEST_VIEW = DEBUG



# --- Allowed Hosts & CSRF ---
ALLOWED_HOSTS = ['.cozywish.com', 'cozywish.onrender.com']
RENDER_EXTERNAL_HOSTNAME = os.environ.get('RENDER_EXTERNAL_HOSTNAME')
if RENDER_EXTERNAL_HOSTNAME:
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)
if DEBUG:
    ALLOWED_HOSTS.extend(['localhost', '127.0.0.1', 'testserver'])

CSRF_TRUSTED_ORIGINS = [
    'https://www.cozywish.com',
    'https://cozywish.onrender.com'
]




# --- Installed Applications ---
INSTALLED_APPS = [
    # Modern Dashboard & Admin Interface (order matters!)
    # 'unfold',                          # Primary modern admin theme
    # 'unfold.contrib.filters',          # Advanced filtering
    # 'unfold.contrib.forms',            # Enhanced form elements
    # 'unfold.contrib.inlines',          # Better inline displays
    # 'unfold.contrib.import_export',    # Import/export functionality
    'admin_tools',                     # Dashboard widgets and tools
    'admin_tools.theming',             # Admin theming
    'admin_tools.menu',                # Custom menu
    'admin_tools.dashboard',           # Dashboard functionality
    'import_export',                   # Data import/export
    'jazzmin',                         # Alternative modern admin theme
    
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',  # For humanize template tags

    # Third-party apps
    'storages',
    'widget_tweaks',
    'crispy_forms',
    'crispy_bootstrap5',  # Upgraded from crispy_bootstrap4
    'formtools',
    'django_htmx',
    'django_cleanup',
    'compressor',  # Static file compression
    'pictures',  # Modern responsive image processing with AVIF/WebP support
    'avatar',  # Profile picture management
    'phonenumber_field',  # International phone number support

    # Wagtail CMS
    'wagtail.contrib.forms',
    'wagtail.contrib.redirects',
    'wagtail.embeds',
    'wagtail.sites',
    'wagtail.users',
    'wagtail.snippets',
    'wagtail.documents',
    'wagtail.images',
    'wagtail.search',
    'wagtail.admin',
    'wagtail',
    'wagtail.contrib.settings',
    'wagtail.contrib.table_block',
    'wagtail.contrib.typed_table_block',
    'wagtail.contrib.styleguide',
    'wagtail.locales',
    'wagtail.contrib.search_promotions',
    'wagtail.contrib.simple_translation',
    'modelcluster',
    'rest_framework',

    # Location Management
    # 'cities_light',  # Modern city/location data - temporarily disabled
    'address',  # Address handling
    # 'geoposition',  # GPS coordinates - incompatible with Django 5.2
    # 'leaflet',  # Interactive maps - requires GDAL system dependency

    # Search and Filtering
    'django_filters',  # Advanced filtering
    'dal',  # Django autocomplete light
    'dal_select2',  # Select2 integration
    'taggit',  # Tag management
    'watson',  # Full-text search

    # Advanced Features
    'django.contrib.sites',  # Required for actstream
    'actstream',  # Activity stream tracking
    'star_ratings',  # Star rating system
    'appointment',  # Advanced booking system

    # Authentication & Security apps
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'allauth.socialaccount.providers.apple',
    'axes',  # Brute force protection
    'django_otp',  # Two-factor authentication
    'django_otp.plugins.otp_totp',
    'django_otp.plugins.otp_static',
    'csp',
    'corsheaders',

    # Project apps
    'accounts_app',
    'utility_app',
    'venues_app',
    'discount_app',
    'booking_cart_app',
    'payments_app',
    'dashboard_app',
    'review_app',
    'notifications_app',
    'admin_app',
    # 'cms_app',  # Wagtail CMS content management - temporarily disabled due to field conflicts
    'utils',
]



# --- Middleware ---
MIDDLEWARE = [
    # CORS headers must be first to handle preflight requests
    'corsheaders.middleware.CorsMiddleware',

    # Security middleware should be early in the stack
    'django.middleware.security.SecurityMiddleware',

    # Axes middleware for brute force protection (after security)
    'axes.middleware.AxesMiddleware',

    # CSP middleware for Content Security Policy
    'csp.middleware.CSPMiddleware',

    # WhiteNoise for static files (after security)
    'whitenoise.middleware.WhiteNoiseMiddleware',

    # Session middleware
    'django.contrib.sessions.middleware.SessionMiddleware',

    # Common middleware for URL processing
    'django.middleware.common.CommonMiddleware',

    # CSRF protection
    'django.middleware.csrf.CsrfViewMiddleware',

    # Authentication
    'django.contrib.auth.middleware.AuthenticationMiddleware',

    # Allauth middleware (after authentication)
    'allauth.account.middleware.AccountMiddleware',

    # OTP middleware for two-factor authentication (after auth)
    'django_otp.middleware.OTPMiddleware',

    # Messages framework
    'django.contrib.messages.middleware.MessageMiddleware',

    # HTMX middleware for dynamic interactions
    'django_htmx.middleware.HtmxMiddleware',

    # Clickjacking protection
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

    # Custom admin CSP middleware (after CSP middleware)
    'admin_app.middleware.AdminCSPMiddleware',

    # Admin performance monitoring (after admin middleware) - temporarily disabled
    # 'django_admin_performance_tools.middleware.AdminPerformanceMiddleware',

    # Wagtail middleware
    'wagtail.contrib.redirects.middleware.RedirectMiddleware',

    # Dashboard access control (last)
    'dashboard_app.middleware.DashboardAccessMiddleware',
]



# --- URL Configuration ---
ROOT_URLCONF = 'project_root.urls'
WSGI_APPLICATION = 'project_root.wsgi.application'



# --- Templates ---
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': False,
        'OPTIONS': {
            'loaders': [
                'admin_tools.template_loaders.Loader',
                'django.template.loaders.filesystem.Loader',
                'django.template.loaders.app_directories.Loader',
            ],
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'admin_app.context_processors.recent_admin_links',
                'booking_cart_app.context_processors.cart_context',
                'notifications_app.context_processors.notifications_context',
                'dashboard_app.context_processors.provider_context',
            ],
        },
    },
]



# --- Database Configuration ---
DATABASE_URL = env('DATABASE_URL', default=None)
if DATABASE_URL:
    DATABASES = {'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)}
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }


# --- Test Database Configuration ---
if 'test' in sys.argv:
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }


# --- Password Validation ---
AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]


# --- Internationalization ---
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True


# --- Testing Flag ---
TESTING = 'test' in sys.argv


# --- Static Files Configuration ---
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

# Add compressor finder to staticfiles finders
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'compressor.finders.CompressorFinder',
]

if not DEBUG:
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = False  # Disable in production for performance

    # Advanced WhiteNoise settings for production
    WHITENOISE_MAX_AGE = 31536000  # 1 year cache for static files
    WHITENOISE_SKIP_COMPRESS_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'zip', 'gz', 'tgz', 'bz2', 'tbz', 'xz', 'br']
    WHITENOISE_IMMUTABLE_FILE_TEST = lambda path, url: True  # Mark all files as immutable for better caching
else:
    # Development settings
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = True


# --- Django Compressor Configuration ---
COMPRESS_ENABLED = not DEBUG  # Enable compression in production
COMPRESS_OFFLINE = not DEBUG  # Pre-compress files in production
COMPRESS_CSS_FILTERS = [
    'compressor.filters.css_default.CssAbsoluteFilter',
    'compressor.filters.cssmin.rCSSMinFilter',
]
COMPRESS_JS_FILTERS = [
    'compressor.filters.jsmin.rJSMinFilter',
]

# Compressor storage and URL settings
COMPRESS_STORAGE = 'compressor.storage.CompressorFileStorage'
COMPRESS_URL = STATIC_URL
COMPRESS_ROOT = STATIC_ROOT

# Cache settings for compressor
COMPRESS_CACHE_BACKEND = 'default'
COMPRESS_CACHE_KEY_FUNCTION = 'compressor.cache.simple_cachekey'

# Output directory for compressed files
COMPRESS_OUTPUT_DIR = 'CACHE'

# Rebuild compressed files when source files change
COMPRESS_REBUILD_TIMEOUT = 2592000  # 30 days

# Advanced compression settings
COMPRESS_PRECOMPILERS = (
    ('text/coffeescript', 'coffee --compile --stdio'),
    ('text/less', 'lessc {infile} {outfile}'),
    ('text/x-sass', 'sass {infile} {outfile}'),
    ('text/x-scss', 'sass --scss {infile} {outfile}'),
)

# Compression parser settings
COMPRESS_PARSER = 'compressor.parser.AutoSelectParser'

# Enable compression for development (optional)
# COMPRESS_ENABLED = True  # Uncomment to enable compression in development

# File extension handling
COMPRESS_CSS_HASHING_METHOD = 'mtime'
COMPRESS_JS_HASHING_METHOD = 'mtime'

# Offline compression settings
COMPRESS_OFFLINE_CONTEXT = {
    'STATIC_URL': STATIC_URL,
}

# Compression manifest settings
COMPRESS_OFFLINE_MANIFEST = 'manifest.json'


# --- Media Files Configuration ---
if DEBUG:
    MEDIA_URL = '/media/'
    MEDIA_ROOT = BASE_DIR / 'media'
    # Use default file system storage for development
    STORAGES = {
        "default": {
            "BACKEND": "django.core.files.storage.FileSystemStorage",
        },
        "staticfiles": {
            "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
        },
    }
else:
    # AWS S3 Configuration
    AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID', default=None)
    AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY', default=None)
    AWS_STORAGE_BUCKET_NAME = env('AWS_STORAGE_BUCKET_NAME', default=None)
    AWS_S3_REGION_NAME = env('AWS_S3_REGION_NAME', default='us-east-1')
    AWS_S3_CUSTOM_DOMAIN = env('AWS_S3_CUSTOM_DOMAIN', default=None)

    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY and AWS_STORAGE_BUCKET_NAME:
        # Django 4.2+ STORAGES configuration (replaces DEFAULT_FILE_STORAGE)
        STORAGES = {
            "default": {
                "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
                "OPTIONS": {
                    "access_key": AWS_ACCESS_KEY_ID,
                    "secret_key": AWS_SECRET_ACCESS_KEY,
                    "bucket_name": AWS_STORAGE_BUCKET_NAME,
                    "region_name": AWS_S3_REGION_NAME,
                    "custom_domain": AWS_S3_CUSTOM_DOMAIN,
                    "file_overwrite": False,
                    "default_acl": None,
                    "signature_version": "s3v4",
                    "addressing_style": "virtual",
                    "use_ssl": True,
                    "verify": True,
                    "object_parameters": {
                        "CacheControl": "max-age=86400",
                    },
                    "querystring_auth": True,
                    "querystring_expire": 3600,  # 1 hour
                },
            },
            "staticfiles": {
                "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
            },
        }

        # Legacy AWS settings for backward compatibility (some packages may still use these)
        # Note: DEFAULT_FILE_STORAGE is deprecated in Django 4.2+ in favor of STORAGES
        AWS_S3_FILE_OVERWRITE = False
        AWS_DEFAULT_ACL = None
        AWS_S3_VERIFY = True
        AWS_S3_USE_SSL = True
        AWS_S3_SIGNATURE_VERSION = 's3v4'
        AWS_S3_ADDRESSING_STYLE = 'virtual'
        AWS_QUERYSTRING_AUTH = True
        AWS_QUERYSTRING_EXPIRE = 3600  # 1 hour

        # Performance and caching
        AWS_S3_OBJECT_PARAMETERS = {
            'CacheControl': 'max-age=86400',
        }

        # URL configuration
        if AWS_S3_CUSTOM_DOMAIN:
            MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/'
        else:
            MEDIA_URL = f'https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com/'

        # Ensure URLs don't have double slashes
        if not MEDIA_URL.endswith('/'):
            MEDIA_URL += '/'

    else:
        # Log missing credentials for debugging
        missing_creds = []
        if not AWS_ACCESS_KEY_ID:
            missing_creds.append('AWS_ACCESS_KEY_ID')
        if not AWS_SECRET_ACCESS_KEY:
            missing_creds.append('AWS_SECRET_ACCESS_KEY')
        if not AWS_STORAGE_BUCKET_NAME:
            missing_creds.append('AWS_STORAGE_BUCKET_NAME')

        error_msg = f"AWS S3 credentials are required in production. Missing: {', '.join(missing_creds)}"
        print(f"WARNING: {error_msg}")  # Log to console instead of raising exception

        # Fallback to local storage with warning
        MEDIA_URL = '/media/'
        MEDIA_ROOT = BASE_DIR / 'media'
        STORAGES = {
            "default": {
                "BACKEND": "django.core.files.storage.FileSystemStorage",
            },
            "staticfiles": {
                "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
            },
        }


# --- Django Pictures Configuration ---
PICTURES = {
    "BREAKPOINTS": {
        "xs": 576,
        "s": 768,
        "m": 992,
        "l": 1200,
        "xl": 1400,
    },
    "GRID_COLUMNS": 12,
    "CONTAINER_WIDTH": 1200,
    "FILE_TYPES": ["AVIF", "WEBP"],  # Modern formats with fallback
    "PIXEL_DENSITIES": [1, 2],
    "USE_PLACEHOLDERS": DEBUG,  # Enable placeholders in development
    "QUEUE_NAME": "pictures",
    "PROCESSOR": "pictures.tasks.process_picture",
}


# --- Email Configuration (SendGrid) ---
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD', default='')
FORCE_EMAIL_BACKEND = env('FORCE_EMAIL_BACKEND', default=False)

# Email backend logic:
# 1. If FORCE_EMAIL_BACKEND is True, use SMTP even in debug mode
# 2. If EMAIL_HOST_PASSWORD is set and we're not in test mode, use SMTP
# 3. Otherwise, use console backend for development
if TESTING:
    # Always use console backend for tests
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
elif FORCE_EMAIL_BACKEND or (EMAIL_HOST_PASSWORD and not DEBUG):
    # Use SMTP backend for production or when forced
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
elif EMAIL_HOST_PASSWORD and DEBUG:
    # In development with SendGrid configured, ask user what they prefer
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
else:
    # Default to console backend for development without email config
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

EMAIL_HOST = env('EMAIL_HOST', default='smtp.sendgrid.net')
EMAIL_PORT = env('EMAIL_PORT')
EMAIL_USE_TLS = env('EMAIL_USE_TLS')
EMAIL_HOST_USER = env('EMAIL_HOST_USER', default='apikey')
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL', default='<EMAIL>')
SERVER_EMAIL = env('SERVER_EMAIL', default='<EMAIL>')
EMAIL_TIMEOUT = 30


# --- User Model and Primary Key ---
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
AUTH_USER_MODEL = 'accounts_app.CustomUser'


# --- Authentication Configuration ---
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
    'axes.backends.AxesBackend',  # Must be last
]

# --- Authentication URLs ---
LOGIN_URL = '/accounts/login/'
LOGOUT_REDIRECT_URL = '/'

# --- Django-Allauth Configuration ---
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_EMAIL_CONFIRMATION_EXPIRE_DAYS = 3
ACCOUNT_LOGIN_ATTEMPTS_LIMIT = 5
ACCOUNT_LOGIN_ATTEMPTS_TIMEOUT = 300  # 5 minutes
ACCOUNT_LOGOUT_ON_GET = False
ACCOUNT_LOGOUT_REDIRECT_URL = '/'
ACCOUNT_SESSION_REMEMBER = True
ACCOUNT_SIGNUP_PASSWORD_ENTER_TWICE = True
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_USER_MODEL_USERNAME_FIELD = None
ACCOUNT_USER_MODEL_EMAIL_FIELD = 'email'

# Custom adapters
ACCOUNT_ADAPTER = 'accounts_app.adapters.CustomAccountAdapter'
SOCIALACCOUNT_ADAPTER = 'accounts_app.adapters.CustomSocialAccountAdapter'

# Social account providers
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
        },
        'OAUTH_PKCE_ENABLED': True,
    },
    'apple': {
        'APP': {
            'client_id': env('APPLE_CLIENT_ID', default=''),
            'secret': env('APPLE_SECRET', default=''),
            'key': env('APPLE_KEY_ID', default=''),
            'certificate_key': env('APPLE_PRIVATE_KEY', default=''),
        }
    }
}

# --- Django-Axes Configuration (Brute Force Protection) ---
AXES_ENABLED = True
AXES_FAILURE_LIMIT = 5
AXES_COOLOFF_TIME = 1  # 1 hour
AXES_RESET_ON_SUCCESS = True
AXES_LOCKOUT_TEMPLATE = 'accounts_app/account_locked.html'
AXES_LOCKOUT_URL = '/accounts/locked/'
AXES_USE_USER_AGENT = True
AXES_LOCK_OUT_BY_COMBINATION_USER_AND_IP = True
AXES_ENABLE_ADMIN = True

# --- Django-OTP Configuration (Two-Factor Authentication) ---
OTP_TOTP_ISSUER = 'CozyWish'
OTP_LOGIN_URL = '/accounts/login/'



# --- Cache Configuration ---
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'cozywish-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# --- Celery Configuration ---
CELERY_BROKER_URL = env('CELERY_BROKER_URL', default='redis://localhost:6379/0')
CELERY_RESULT_BACKEND = env('CELERY_RESULT_BACKEND', default='redis://localhost:6379/0')
if 'test' in sys.argv:
    CELERY_TASK_ALWAYS_EAGER = True
    CELERY_TASK_EAGER_PROPAGATES = True


# --- Crispy Forms Configuration ---
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# --- Django Avatar Configuration ---
AVATAR_STORAGE_DIR = "avatars"
AVATAR_RESIZE = 300
AVATAR_MAX_AVATARS_PER_USER = 1
AVATAR_MAX_SIZE = 1024 * 1024 * 5  # 5MB
AVATAR_THUMB_FORMAT = "JPEG"
AVATAR_THUMB_QUALITY = 95
AVATAR_DEFAULT_URL = "img/default-avatar.png"

# --- Phone Number Field Configuration ---
PHONENUMBER_DEFAULT_REGION = "US"
PHONENUMBER_DEFAULT_FORMAT = "NATIONAL"

# --- HTMX Configuration ---
# HTMX settings for dynamic interactions
HTMX_CSRF_HEADER_NAME = 'X-CSRFToken'

# --- Django Form Tools Configuration ---
# Session-based form wizard storage
FORM_RENDERER = 'django.forms.renderers.TemplatesSetting'


# --- Content Security Policy (CSP) Configuration ---
# Base CSP settings - can be overridden in environment-specific settings
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = (
    "'self'",
    "'unsafe-inline'",  # Required for some admin functionality and HTMX
    "https://cdn.jsdelivr.net",  # CDN for libraries
    "https://cdnjs.cloudflare.com",  # CDN for libraries
    "https://unpkg.com",  # HTMX CDN
)
CSP_STYLE_SRC = (
    "'self'",
    "'unsafe-inline'",  # Required for admin and some styling
    "https://fonts.googleapis.com",  # Google Fonts
    "https://cdn.jsdelivr.net",  # CDN for CSS libraries
    "https://cdnjs.cloudflare.com",  # CDN for CSS libraries
)
CSP_FONT_SRC = (
    "'self'",
    "https://fonts.gstatic.com",  # Google Fonts
    "data:",  # Data URLs for fonts
)
CSP_IMG_SRC = (
    "'self'",
    "data:",  # Data URLs for images
    "https:",  # Allow HTTPS images (for user uploads, external images)
)
CSP_CONNECT_SRC = (
    "'self'",
    # Add your API endpoints here
)
CSP_FRAME_SRC = (
    "'none'",  # Prevent framing by default
)
CSP_OBJECT_SRC = ("'none'",)  # Prevent object/embed tags
CSP_BASE_URI = ("'self'",)  # Restrict base tag
CSP_FORM_ACTION = ("'self'",)  # Restrict form submissions

# CSP reporting (can be enabled in production)
# CSP_REPORT_URI = '/csp-report/'
CSP_INCLUDE_NONCE_IN = ['script-src', 'style-src']  # Enable nonces for scripts/styles


# --- Rate Limiting Configuration ---
# Django-ratelimit settings
RATELIMIT_ENABLE = True
RATELIMIT_USE_CACHE = 'default'

# Rate limiting rules (can be overridden in environment-specific settings)
RATELIMIT_LOGIN_ATTEMPTS = '5/5m'  # 5 attempts per 5 minutes
RATELIMIT_API_CALLS = '100/h'      # 100 API calls per hour
RATELIMIT_FORM_SUBMISSIONS = '10/m'  # 10 form submissions per minute
RATELIMIT_PASSWORD_RESET = '3/h'   # 3 password reset attempts per hour
RATELIMIT_REGISTRATION = '5/h'     # 5 registration attempts per hour

# Rate limiting view configuration
RATELIMIT_VIEW = 'django_ratelimit.views.ratelimited'


# --- CORS Configuration ---
# Base CORS settings - can be overridden in environment-specific settings
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    # Add your frontend domains here
    # "https://yourdomain.com",
    # "https://www.yourdomain.com",
]

CORS_ALLOWED_ORIGIN_REGEXES = [
    # Add regex patterns for dynamic subdomains if needed
    # r"^https://\w+\.yourdomain\.com$",
]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# CORS preflight cache
CORS_PREFLIGHT_MAX_AGE = 86400  # 24 hours


# --- Security Headers Configuration ---
# Basic security headers (applied in all environments)
SECURE_CONTENT_TYPE_NOSNIFF = True  # Prevent MIME type sniffing
SECURE_BROWSER_XSS_FILTER = True    # Enable XSS filtering
X_FRAME_OPTIONS = 'DENY'            # Prevent clickjacking

# Referrer Policy - control referrer information
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Additional security headers via custom middleware or django-security
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'

# Cookie security settings
SESSION_COOKIE_HTTPONLY = True      # Prevent JavaScript access to session cookies
CSRF_COOKIE_HTTPONLY = True         # Prevent JavaScript access to CSRF cookies
SESSION_COOKIE_SAMESITE = 'Lax'     # CSRF protection
CSRF_COOKIE_SAMESITE = 'Lax'        # CSRF protection

# --- Production Security Settings ---
if not DEBUG:
    # SSL/HTTPS settings
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True

    # HSTS (HTTP Strict Transport Security)
    SECURE_HSTS_SECONDS = 31536000      # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True

    # Additional production security
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

    # Permissions Policy (formerly Feature Policy)
    PERMISSIONS_POLICY = {
        'accelerometer': [],
        'ambient-light-sensor': [],
        'autoplay': [],
        'battery': [],
        'camera': [],
        'cross-origin-isolated': [],
        'display-capture': [],
        'document-domain': [],
        'encrypted-media': [],
        'execution-while-not-rendered': [],
        'execution-while-out-of-viewport': [],
        'fullscreen': [],
        'geolocation': [],
        'gyroscope': [],
        'magnetometer': [],
        'microphone': [],
        'midi': [],
        'navigation-override': [],
        'payment': [],
        'picture-in-picture': [],
        'publickey-credentials-get': [],
        'screen-wake-lock': [],
        'sync-xhr': [],
        'usb': [],
        'web-share': [],
        'xr-spatial-tracking': [],
    }


# --- Forms URL Field Configuration ---
FORMS_URLFIELD_ASSUME_HTTPS = True


# --- Location Management Configuration ---
# Django Cities Light
CITIES_LIGHT_TRANSLATION_LANGUAGES = ['en']
CITIES_LIGHT_INCLUDE_COUNTRIES = ['US']  # Focus on US locations
CITIES_LIGHT_INCLUDE_CITY_TYPES = ['PPL', 'PPLA', 'PPLA2', 'PPLA3', 'PPLA4', 'PPLC']

# Django Leaflet - Commented out due to GDAL dependency
# LEAFLET_CONFIG = {
#     'DEFAULT_CENTER': (39.8283, -98.5795),  # Center of US
#     'DEFAULT_ZOOM': 4,
#     'MIN_ZOOM': 3,
#     'MAX_ZOOM': 18,
#     'TILES': [
#         ('OpenStreetMap', 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
#             'attribution': '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
#         }),
#     ],
#     'SPATIAL_EXTENT': [-180, -90, 180, 90],
#     'SRID': 4326,
#     'PLUGINS': {
#         'forms': {
#             'auto-include': True
#         }
#     }
# }

# Django Taggit
TAGGIT_CASE_INSENSITIVE = True
TAGGIT_TAGS_FROM_STRING = 'taggit.utils.parse_tags'
TAGGIT_STRING_FROM_TAGS = 'taggit.utils.edit_string_for_tags'

# Django Watson Search
WATSON_POSTGRESQL_SEARCH_CONFIG = 'english'


# --- Advanced Features Configuration ---

# Sites Framework (required for actstream)
SITE_ID = 1

# Activity Stream Settings
ACTSTREAM_SETTINGS = {
    'MANAGER': 'actstream.managers.ActionManager',
    'FETCH_RELATIONS': True,
    'USE_PREFETCH': True,
}

# Star Ratings Settings
STAR_RATINGS_ANONYMOUS = True  # Allow anonymous ratings
STAR_RATINGS_RANGE = 5  # 1-5 star rating
STAR_RATINGS_STAR_HEIGHT = 32  # Star height in pixels
STAR_RATINGS_STAR_WIDTH = 32  # Star width in pixels
STAR_RATINGS_RERATE = True  # Allow users to change their rating
STAR_RATINGS_RERATE_SAME_DELETE = True  # Delete rating if same rating clicked

# Django Appointment Settings
APPOINTMENT_WEBSITE_NAME = "CozyWish"
APPOINTMENT_ADMIN_BASE_TEMPLATE = "admin/base.html"
APPOINTMENT_BASE_TEMPLATE = "base.html"
APPOINTMENT_SLOT_DURATION = 30  # Default slot duration in minutes
APPOINTMENT_LEAD_TIME = 24  # Minimum hours before appointment
APPOINTMENT_FINISH_TIME = 24  # Maximum hours for appointment
APPOINTMENT_BUFFER_TIME = 15  # Buffer time between appointments in minutes


# --- Django Jazzmin Configuration ---
JAZZMIN_SETTINGS = {
    "site_title": "CozyWish Admin",
    "site_header": "CozyWish Administration",
    "site_brand": "CozyWish",
    "site_logo": "img/logo.png",
    "login_logo": "img/logo.png",
    "login_logo_dark": "img/logo-dark.png",
    "site_logo_classes": "img-circle",
    "site_icon": "img/favicon.ico",
    "welcome_sign": "Welcome to CozyWish Admin",
    "copyright": "CozyWish Ltd",
    "search_model": ["auth.User", "venues_app.Venue"],
    "user_avatar": "avatar",
    
    # Top Menu
    "topmenu_links": [
        {"name": "Home", "url": "admin:index", "permissions": ["auth.view_user"]},
        {"name": "Support", "url": "https://cozywish.com/support", "new_window": True},
        {"model": "auth.User"},
        {"app": "venues_app"},
    ],
    
    # User Menu
    "usermenu_links": [
        {"name": "Support", "url": "https://cozywish.com/support", "new_window": True},
        {"model": "auth.user"}
    ],
    
    # Side Menu
    "show_sidebar": True,
    "navigation_expanded": True,
    "hide_apps": [],
    "hide_models": [],
    "order_with_respect_to": ["auth", "venues_app", "booking_cart_app", "payments_app", "admin_app"],
    
    # Custom Links
    "custom_links": {
        "admin_app": [
            {
                "name": "Dashboard", 
                "url": "admin_app:admin_dashboard", 
                "icon": "fas fa-tachometer-alt",
                "permissions": ["admin_app.view_dashboard"]
            },
            {
                "name": "User Management", 
                "url": "admin_app:user_list", 
                "icon": "fas fa-users",
                "permissions": ["auth.view_user"]
            },
            {
                "name": "System Health", 
                "url": "admin_app:system_health_logs", 
                "icon": "fas fa-heartbeat",
                "permissions": ["admin_app.view_systemhealthlog"]
            },
        ]
    },
    
    # Icons
    "icons": {
        "auth": "fas fa-users-cog",
        "auth.user": "fas fa-user",
        "auth.Group": "fas fa-users",
        "venues_app": "fas fa-building",
        "venues_app.Venue": "fas fa-store",
        "venues_app.VenueType": "fas fa-tags",
        "booking_cart_app": "fas fa-shopping-cart",
        "booking_cart_app.Booking": "fas fa-calendar-check",
        "booking_cart_app.BookingCart": "fas fa-shopping-cart",
        "payments_app": "fas fa-credit-card",
        "payments_app.Payment": "fas fa-money-bill-wave",
        "review_app": "fas fa-star",
        "review_app.Review": "fas fa-comment-dots",
        "discount_app": "fas fa-percent",
        "discount_app.Discount": "fas fa-tag",
        "notifications_app": "fas fa-bell",
        "notifications_app.Notification": "fas fa-envelope",
        "admin_app": "fas fa-cog",
        "admin_app.StaticPage": "fas fa-file-alt",
        "admin_app.BlogPost": "fas fa-blog",
        "admin_app.HomepageBlock": "fas fa-th-large",
    },
    
    # Default Icon
    "default_icon_parents": "fas fa-chevron-circle-right",
    "default_icon_children": "fas fa-circle",
    
    # Related Modal
    "related_modal_active": False,
    
    # Custom CSS & JS
    "custom_css": None,
    "custom_js": None,
    
    # Show/Hide Elements
    "show_ui_builder": False,
    "changeform_format": "horizontal_tabs",
    "changeform_format_overrides": {
        "auth.user": "collapsible",
        "auth.group": "vertical_tabs"
    },
    "language_chooser": True,
}

JAZZMIN_UI_TWEAKS = {
    "navbar_small_text": False,
    "footer_small_text": False,
    "body_small_text": False,
    "brand_small_text": False,
    "brand_colour": "navbar-primary",
    "accent": "accent-primary",
    "navbar": "navbar-primary navbar-dark",
    "no_navbar_border": False,
    "navbar_fixed": False,
    "layout_boxed": False,
    "footer_fixed": False,
    "sidebar_fixed": False,
    "sidebar": "sidebar-dark-primary",
    "sidebar_nav_small_text": False,
    "sidebar_disable_expand": False,
    "sidebar_nav_child_indent": False,
    "sidebar_nav_compact_style": False,
    "sidebar_nav_legacy_style": False,
    "sidebar_nav_flat_style": False,
    "theme": "default",
    "dark_mode_theme": None,
    "button_classes": {
        "primary": "btn-primary",
        "secondary": "btn-secondary",
        "info": "btn-info",
        "warning": "btn-warning",
        "danger": "btn-danger",
        "success": "btn-success"
    },
    "actions_sticky_top": False
}

# --- Django Admin Performance Tools Configuration ---
ADMIN_PERFORMANCE_TOOLS = {
    'ENABLE_STATS': True,
    'ENABLE_CACHE': True,
    'CACHE_KEY_PREFIX': 'admin_perf',
    'CACHE_TIMEOUT': 300,  # 5 minutes
    'ENABLE_LOGGING': True,
    'LOG_SLOW_QUERIES': True,
    'SLOW_QUERY_THRESHOLD': 0.5,  # seconds
}

# Django Cache Configuration (for performance tools)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 300,  # 5 minutes
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# --- Django Unfold Configuration ---
UNFOLD = {
    "SITE_TITLE": "CozyWish Admin",
    "SITE_HEADER": "CozyWish Administration",
    "SITE_URL": "/",
    "SITE_ICON": {
        "light": "img/favicon.ico",
        "dark": "img/favicon.ico",
    },
    "SITE_LOGO": {
        "light": "img/logo.png",
        "dark": "img/logo-dark.png",
    },
    "SITE_SYMBOL": "speed",
    "SHOW_HISTORY": True,
    "SHOW_VIEW_ON_SITE": True,
    "ENVIRONMENT": "CozyWish Admin",
    "DASHBOARD_CALLBACK": "admin_app.admin.dashboard_callback",
    "LOGIN": {
        "image": "img/admin-login-bg.jpg",
        "redirect_after": "/admin/",
    },
    "STYLES": [
        lambda request: static("css/admin-custom.css"),
    ],
    "SCRIPTS": [
        lambda request: static("js/admin-custom.js"),
    ],
    "COLORS": {
        "primary": {
            "50": "#fef7f0",
            "100": "#fae1d7",
            "200": "#f4c2a7",
            "300": "#ed9d6f",
            "400": "#e67e22",
            "500": "#d35400",
            "600": "#b7471d",
            "700": "#95391a",
            "800": "#7a2e1a",
            "900": "#652818",
            "950": "#37130a"
        }
    },
    "EXTENSIONS": {
        "modeltranslation": {
            "flags": {
                "en": "🇺🇸",
                "fr": "🇫🇷",
                "nl": "🇳🇱",
            },
        },
    },
    "SIDEBAR": {
        "show_search": True,
        "show_all_applications": True,
        "navigation": [
            {
                "title": "Navigation",
                "separator": True,
                "items": [
                    {
                        "title": "Dashboard",
                        "icon": "dashboard",
                        "link": reverse_lazy("admin:index"),
                    },
                    {
                        "title": "Users & Authentication",
                        "icon": "people",
                        "link": reverse_lazy("admin:auth_user_changelist"),
                    },
                    {
                        "title": "Venues",
                        "icon": "business",
                        "link": reverse_lazy("admin:venues_app_venue_changelist"),
                    },
                    {
                        "title": "Bookings",
                        "icon": "event",
                        "link": reverse_lazy("admin:booking_cart_app_booking_changelist"),
                    },
                ],
            },
        ],
    },
    "TABS": [
        {
            "models": [
                "auth.user",
                "auth.group",
            ],
            "items": [
                {
                    "title": "Users",
                    "link": reverse_lazy("admin:auth_user_changelist"),
                },
                {
                    "title": "Groups",
                    "link": reverse_lazy("admin:auth_group_changelist"),
                },
            ],
        },
    ],
}

# --- Django Admin Tools Configuration ---
ADMIN_TOOLS_MENU = 'admin_app.menu.CustomMenu'
ADMIN_TOOLS_INDEX_DASHBOARD = 'admin_app.dashboard.CustomIndexDashboard'
ADMIN_TOOLS_APP_INDEX_DASHBOARD = 'admin_app.dashboard.CustomAppIndexDashboard'

# --- Django Import Export Configuration ---
IMPORT_EXPORT_USE_TRANSACTIONS = True
IMPORT_EXPORT_SKIP_ADMIN_LOG = False
IMPORT_EXPORT_TMP_STORAGE_CLASS = 'import_export.tmp_storages.TempFolderStorage'
IMPORT_EXPORT_ESCAPE_HTML_ON_EXPORT = True


# --- Wagtail CMS Configuration ---
WAGTAIL_SITE_NAME = "CozyWish CMS"
WAGTAIL_ENABLE_UPDATE_CHECK = False
WAGTAIL_USAGE_COUNT_ENABLED = False
WAGTAIL_GRAVATAR_PROVIDER_URL = '//www.gravatar.com/avatar'
WAGTAIL_FRONTEND_LOGIN_URL = '/accounts/login/'
WAGTAIL_FRONTEND_LOGIN_TEMPLATE = 'account/login.html'
WAGTAIL_APPEND_SLASH = True
WAGTAIL_PASSWORD_MANAGEMENT_ENABLED = True
WAGTAIL_PASSWORD_RESET_ENABLED = True
WAGTAIL_ENABLE_WHATS_NEW_BANNER = False

# Wagtail Rich Text Editor
WAGTAILIMAGES_EXTENSIONS = ['gif', 'jpg', 'jpeg', 'png', 'webp', 'svg']
WAGTAILIMAGES_MAX_UPLOAD_SIZE = 20 * 1024 * 1024  # 20MB
WAGTAILIMAGES_MAX_IMAGE_PIXELS = *********  # 128 megapixels
WAGTAILIMAGES_FEATURE_DETECTION_ENABLED = True
WAGTAILIMAGES_JPEG_QUALITY = 85
WAGTAILIMAGES_WEBP_QUALITY = 85
WAGTAILIMAGES_AVIF_QUALITY = 85

# Wagtail Search
WAGTAILSEARCH_BACKENDS = {
    'default': {
        'BACKEND': 'wagtail.search.backends.database',
        'SEARCH_CONFIG': 'english',
    }
}

# Wagtail Admin
WAGTAILADMIN_BASE_URL = 'https://cozywish.com'
WAGTAILADMIN_EXTERNAL_LINK_CONVERSION = 'confirm'
WAGTAILADMIN_NOTIFICATION_FROM_EMAIL = '<EMAIL>'
WAGTAILADMIN_NOTIFICATION_USE_HTML = True
WAGTAILADMIN_NOTIFICATION_INCLUDE_SUPERUSERS = True
WAGTAILADMIN_RECENT_EDITS_LIMIT = 10
WAGTAILADMIN_USER_CREATION_ENABLED = True
WAGTAILADMIN_USER_EDITING_ENABLED = True
WAGTAILADMIN_ACCOUNT_MANAGEMENT_ENABLED = True
WAGTAILADMIN_LOCALE_BLOCK_CONTENT = True
WAGTAILADMIN_GLOBAL_SIDE_PANELS = [
    'wagtail.admin.panels.PageExplorerPanel',
    'wagtail.admin.panels.TasksPanel',
]

# Django REST Framework (required by Wagtail)
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20
}

# --- Django Unfold Configuration ---
from django.templatetags.static import static
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

UNFOLD = {
    "SITE_TITLE": "CozyWish Admin",
    "SITE_HEADER": "CozyWish Management Dashboard",
    "SITE_URL": "/",
    "SITE_ICON": {
        "light": lambda request: static("img/cozywish-icon-light.svg"),
        "dark": lambda request: static("img/cozywish-icon-dark.svg"),
    },
    "SITE_LOGO": {
        "light": lambda request: static("img/cozywish-logo-light.svg"),
        "dark": lambda request: static("img/cozywish-logo-dark.svg"),
    },
    "SITE_SYMBOL": "dashboard",
    "SHOW_HISTORY": True,
    "SHOW_VIEW_ON_SITE": True,
    "THEME": None,  # None = auto dark/light mode, "dark" or "light" to force
    "LOGIN": {
        "image": lambda request: static("img/admin-login-bg.jpg"),
        "redirect_after": lambda request: reverse_lazy("admin:index"),
    },
    "STYLES": [
        lambda request: static("css/unfold-custom.css"),
    ],
    "SCRIPTS": [
        lambda request: static("js/unfold-custom.js"),
    ],
    "COLORS": {
        "primary": {
            "50": "249 250 251",
            "100": "243 244 246", 
            "200": "229 231 235",
            "300": "209 213 219",
            "400": "156 163 175",
            "500": "107 114 128",
            "600": "75 85 99",
            "700": "55 65 81",
            "800": "31 41 55",
            "900": "17 24 39",
            "950": "3 7 18",
        },
    },
    "SIDEBAR": {
        "show_search": True,
        "show_all_applications": True,
        "navigation": [
            {
                "title": _("Dashboard"),
                "separator": True,
                "items": [
                    {
                        "title": _("Analytics Dashboard"),
                        "icon": "dashboard",
                        "link": reverse_lazy("admin:index"),
                    },
                    {
                        "title": _("System Health"),
                        "icon": "monitor_heart",
                        "link": reverse_lazy("dashboard_app:admin_system_health"),
                        "permission": lambda request: request.user.is_superuser,
                    },
                ],
            },
            {
                "title": _("User Management"),
                "separator": True,
                "items": [
                    {
                        "title": _("Users"),
                        "icon": "people",
                        "link": reverse_lazy("admin:accounts_app_customuser_changelist"),
                    },
                    {
                        "title": _("Customer Profiles"),
                        "icon": "person",
                        "link": reverse_lazy("admin:accounts_app_customerprofile_changelist"),
                    },
                    {
                        "title": _("Service Providers"),
                        "icon": "business",
                        "link": reverse_lazy("admin:accounts_app_serviceproviderprofile_changelist"),
                    },
                ],
            },
            {
                "title": _("Business Management"),
                "separator": True,
                "items": [
                    {
                        "title": _("Venues"),
                        "icon": "store",
                        "link": reverse_lazy("admin:venues_app_venue_changelist"),
                    },
                    {
                        "title": _("Bookings"),
                        "icon": "event",
                        "link": reverse_lazy("admin:booking_cart_app_booking_changelist"),
                    },
                    {
                        "title": _("Payments"),
                        "icon": "payments",
                        "link": reverse_lazy("admin:payments_app_payment_changelist"),
                    },
                    {
                        "title": _("Reviews"),
                        "icon": "star",
                        "link": reverse_lazy("admin:review_app_review_changelist"),
                    },
                ],
            },
        ],
    },
}

# --- Django Admin Charts Configuration ---
ADMIN_CHARTS_NVD3_JS_PATH = 'nvd3/build/nv.d3.js'
ADMIN_CHARTS_NVD3_CSS_PATH = 'nvd3/build/nv.d3.css'
ADMIN_CHARTS_D3_JS_PATH = 'd3/d3.js'

"""
## Production Environment Variables
AWS_ACCESS_KEY_ID
AWS_S3_CUSTOM_DOMAIN
AWS_S3_REGION_NAME
AWS_SECRET_ACCESS_KEY
AWS_STORAGE_BUCKET_NAME
DATABASE_URL
DEBUG
EMAIL_HOST
EMAIL_HOST_PASSWORD
EMAIL_HOST_USER
EMAIL_PORT
EMAIL_USE_TLS
LOG_LEVEL
SECRET_KEY
WEB_CONCURRENCY
"""