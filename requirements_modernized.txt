# CozyWish Modernized Requirements
# Updated: 2025-07-05
# Django 5.2.3 Compatible Packages

# ===== CORE DJANGO =====
Django==5.2.3
asgiref==3.8.1
sqlparse==0.5.3
tzdata==2025.2

# ===== DATABASE =====
psycopg2-binary==2.9.10
dj-database-url==3.0.0

# ===== ENVIRONMENT & CONFIGURATION =====
django-environ==0.11.2              # NEW: Better environment management
python-decouple==3.8                # KEEP: Existing config (migrate to django-environ)
python-dotenv==1.0.1

# ===== AUTHENTICATION & AUTHORIZATION =====
django-allauth==65.4.0               # NEW: Social authentication & user management
django-guardian==2.4.0               # NEW: Object-level permissions
django-otp==1.5.4                    # NEW: Two-factor authentication
django-simple-history==3.8.0         # NEW: Model change tracking

# ===== REST API FRAMEWORK =====
djangorestframework==3.15.2          # NEW: REST API framework
djangorestframework-simplejwt==5.3.0 # NEW: JWT authentication
drf-spectacular==0.27.2              # NEW: API documentation
django-cors-headers==4.4.0           # NEW: CORS support
django-filter==24.3                  # NEW: Advanced filtering

# ===== FORMS & UI =====
django-crispy-forms==2.4             # UPGRADE: From 2.4
crispy-bootstrap5==2024.10           # NEW: Bootstrap 5 support (replace crispy-bootstrap4)
django-widget-tweaks==1.5.0          # KEEP: Existing
django-formtools==2.5.1              # NEW: Multi-step forms
django-htmx==1.19.0                  # NEW: Dynamic UI interactions

# ===== SEARCH & FILTERING =====
django-elasticsearch-dsl==8.0        # NEW: Elasticsearch integration

# ===== CACHING & PERFORMANCE =====
django-redis==5.4.0                  # NEW: Redis caching
django-silk==5.2.0                   # NEW: Database profiling
django-db-pool==1.0.4                # NEW: Connection pooling
django-compressor==4.5.1             # NEW: Static file compression
django-prometheus==2.3.1             # NEW: Performance metrics

# ===== SECURITY =====
django-security==0.18.0              # NEW: Security headers
django-csp==3.8                      # NEW: Content Security Policy
django-ratelimit==4.1.0              # NEW: Rate limiting
django-auditlog==3.0.0               # NEW: Audit logging

# ===== MONITORING & HEALTH =====
django-health-check==3.18.3          # NEW: Health monitoring
sentry-sdk[django]==2.18.0           # NEW: Error tracking
django-dbbackup==4.2.0               # NEW: Backup automation

# ===== DEVELOPMENT TOOLS =====
django-debug-toolbar==4.4.6          # NEW: Development debugging
django-extensions==3.2.3             # NEW: Enhanced management commands

# ===== FILE HANDLING =====
django-storages==1.14.6              # KEEP: AWS S3 integration
django-cleanup==8.1.0                # NEW: Automatic file cleanup
boto3==1.38.36                       # KEEP: AWS SDK
botocore==1.38.36                    # KEEP: AWS core
s3transfer==0.13.0                   # KEEP: S3 transfer

# ===== TASK QUEUE =====
celery==5.5.3                        # KEEP: Task queue
redis==5.2.0                         # NEW: Redis client
kombu==5.5.4                         # KEEP: Celery dependency
billiard==4.2.1                      # KEEP: Celery dependency
vine==5.1.0                          # KEEP: Celery dependency
amqp==5.3.1                          # KEEP: Celery dependency

# ===== EMAIL =====
sendgrid==6.12.4                     # KEEP: Email service
python-http-client==3.3.7            # KEEP: SendGrid dependency

# ===== TESTING =====
pytest==8.4.0                        # KEEP: Testing framework
pytest-django==4.9.0                 # NEW: Django-specific pytest features
pytest-cov==6.1.1                    # KEEP: Coverage
pytest-asyncio==1.0.0                # KEEP: Async testing
factory-boy==3.3.1                   # NEW: Test data factories
faker==30.8.2                        # NEW: Fake data generation
model-bakery==1.20.5                 # KEEP: Test model creation
coverage==7.8.2                      # KEEP: Coverage reporting

# ===== BROWSER TESTING =====
playwright==1.48.0                   # KEEP: Browser automation
pytest-playwright==0.5.2             # KEEP: Playwright pytest integration
pytest-base-url==2.1.0               # KEEP: Base URL testing

# ===== CODE QUALITY =====
black==24.10.0                       # NEW: Code formatting
isort==5.13.2                        # NEW: Import sorting
flake8==7.1.1                        # NEW: Linting
pre-commit==4.0.1                    # NEW: Git hooks

# ===== UTILITIES =====
python-slugify==8.0.4                # KEEP: Slug generation
python-dateutil==2.9.0.post0         # KEEP: Date utilities
pillow==11.2.1                       # KEEP: Image processing
requests==2.32.4                     # KEEP: HTTP requests
beautifulsoup4==4.13.4               # KEEP: HTML parsing
bleach==6.2.0                        # KEEP: HTML sanitization

# ===== STATIC FILES =====
whitenoise==6.9.0                    # KEEP: Static file serving
Brotli==1.1.0                        # KEEP: Compression

# ===== PRODUCTION SERVER =====
gunicorn==23.0.0                     # KEEP: WSGI server
uvicorn==0.34.2                      # KEEP: ASGI server

# ===== REPORTING =====
reportlab==4.4.1                     # KEEP: PDF generation

# ===== DEPENDENCIES =====
certifi==2025.4.26                   # KEEP: SSL certificates
charset-normalizer==3.4.2            # KEEP: Character encoding
chardet==5.2.0                       # KEEP: Character detection
click==8.2.1                         # KEEP: CLI framework
click-didyoumean==0.3.1              # KEEP: CLI suggestions
click-plugins==1.1.1                 # KEEP: CLI plugins
click-repl==0.3.0                    # KEEP: CLI REPL
greenlet==3.1.1                      # KEEP: Async support
h11==0.16.0                          # KEEP: HTTP/1.1 protocol
idna==3.10                           # KEEP: Internationalized domain names
iniconfig==2.1.0                     # KEEP: INI config parsing
jmespath==1.0.1                      # KEEP: JSON path expressions
MarkupSafe==3.0.2                    # KEEP: Safe string handling
packaging==25.0                      # KEEP: Package utilities
pluggy==1.6.0                        # KEEP: Plugin system
prompt_toolkit==3.0.51               # KEEP: Interactive prompts
pyee==12.0.0                         # KEEP: Event emitter
Pygments==2.19.1                     # KEEP: Syntax highlighting
six==1.17.0                          # KEEP: Python 2/3 compatibility
soupsieve==2.7                       # KEEP: CSS selectors
text-unidecode==1.3                  # KEEP: Unicode to ASCII
typing_extensions==4.13.2            # KEEP: Type hints
urllib3==2.4.0                       # KEEP: HTTP library
wcwidth==0.2.13                      # KEEP: Terminal width
webencodings==0.5.1                  # KEEP: Web encodings
Werkzeug==3.1.3                      # KEEP: WSGI utilities
ecdsa==0.19.1                        # KEEP: Cryptography

# ===== REMOVED PACKAGES =====
# crispy-bootstrap4==2024.10         # REMOVED: Replaced with crispy-bootstrap5

# ===== NOTES =====
# 1. All packages are Django 5.2.3 compatible
# 2. Versions are pinned to latest stable releases as of 2025-07-05
# 3. New packages marked with "# NEW:" comment
# 4. Upgraded packages marked with "# UPGRADE:" comment
# 5. Existing packages marked with "# KEEP:" comment
# 6. Test in development environment before production deployment

# ===== DISCOUNT & PROMOTIONS PACKAGES =====
django-pydantic-field==0.3.12           # NEW: Pydantic integration for discount validation schemas
pydantic==2.10.3                        # NEW: Data validation using Python type annotations
django-money==3.5.3                     # NEW: Money fields for accurate discount calculations
django-lifecycle==1.2.4                 # NEW: Model lifecycle hooks for discount events
# NOTE: Using custom business rules implementation instead of problematic external packages
